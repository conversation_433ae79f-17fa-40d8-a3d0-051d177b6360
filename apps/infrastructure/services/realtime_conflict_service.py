"""Real-Time Conflict Detection Service with WebSocket Integration
from typing import Dict
from typing import Type
import channels
import hashlib
import json
import logging
import logging
logger = logging.getLogger(__name__)
import re
import time

Advanced real-time conflict detection that integrates with WebSockets for
live notifications during drawing operations. Provides instant feedback
to users and real-time collaboration features.
"""

from __future__ import annotations

import json
import logging
from datetime import timed<PERSON><PERSON>
from typing import Any

from django.core.cache import cache
from django.utils import timezone

from asgiref.sync import sync_to_async
from channels.db import database_sync_to_async
from channels.generic.websocket import AsyncWebsocketConsumer

from apps.infrastructure.models.conflict_rules import ConflictDetectionEvent
from apps.infrastructure.models.spatial import Utility

logger = logging.getLogger(__name__)


class RealTimeConflictService:
    """Real-time conflict detection service for live drawing operations.

    Features:
    - Instant conflict detection during drawing
    - WebSocket notifications to connected clients
    - Collaborative conflict resolution
    - Performance optimization with smart caching
    - User presence tracking

    Args:
    ----
        project_id: UUID string of the project this service manages

    """

    def __init__(self, project_id: str) -> None:
        """Initialize real-time service for a project.

        Args:
        ----
            project_id: UUID string of the project to monitor for conflicts

        """
        self.project_id = project_id
        self.active_users: set[str] = set()
        self.drawing_sessions: dict[str, dict[str, Any]] = {}
        self.conflict_cache: dict[str, Any] = {}
        self.performance_metrics: dict[str, float] = {
            "total_detections": 0,
            "avg_detection_time": 0,
            "cache_hit_rate": 0,
        }

    async def user_connected(self, user_id: str, channel_name: str) -> None:
        """Handle user connection to real-time service.

        Args:
        ----
            user_id: Unique identifier for the connecting user
            channel_name: WebSocket channel name for this user's connection

        """
        self.active_users.add(user_id)

        # Initialize user drawing session
        self.drawing_sessions[user_id] = {
            "channel_name": channel_name,
            "connected_at": timezone.now(),
            "last_activity": timezone.now(),
            "active_drawings": [],
            "conflict_count": 0,
        }

        # Send welcome message with current project status
        await self._send_to_user(
            user_id,
            {
                "type": "connection_established",
                "project_id": self.project_id,
                "active_users": len(self.active_users),
                "timestamp": timezone.now().isoformat(),
            },
        )

        # Notify other users of new connection
        await self._broadcast_to_others(
            user_id,
            {
                "type": "user_joined",
                "user_id": user_id,
                "active_users": len(self.active_users),
            },
        )

        logger.info(f"User {user_id} connected to real-time conflict service for project {self.project_id}")

    async def user_disconnected(self, user_id: str) -> None:
        """Handle user disconnection.

        Args:
        ----
            user_id: Unique identifier for the disconnecting user

        """
        if user_id in self.active_users:
            self.active_users.remove(user_id)

        if user_id in self.drawing_sessions:
            del self.drawing_sessions[user_id]

        # Notify other users of disconnection
        await self._broadcast_to_others(
            user_id,
            {
                "type": "user_left",
                "user_id": user_id,
                "active_users": len(self.active_users),
            },
        )

        logger.info(f"User {user_id} disconnected from real-time conflict service")

    async def detect_conflicts_real_time(
        self,
        user_id: str,
        utility_data: dict[str, Any],
        geometry_data: dict[str, Any],
    ) -> dict[str, Any]:
        """Perform real-time conflict detection for drawing operations.

        Args:
        ----
            user_id: ID of the user performing the action
            utility_data: Utility information being drawn/modified
            geometry_data: Geometry data for the utility

        Returns:
        -------
            Dict containing conflict detection results

        """
        start_time = timezone.now()

        try:
            # Update user activity
            if user_id in self.drawing_sessions:
                self.drawing_sessions[user_id]["last_activity"] = timezone.now()

            # Import runtime dependencies
            from .conflict_detection_engine import (
                ConflictDetectionEngine,
                DetectionContext,
            )

            # Create detection context for real-time operation
            context = DetectionContext(
                user_id=int(user_id) if user_id.isdigit() else None,
                drawing_mode=True,
                real_time=True,
                performance_mode="fast",  # Optimize for speed in real-time
            )

            # Check cache first for similar geometries
            cache_key = self._generate_cache_key(utility_data, geometry_data)
            cached_result = cache.get(cache_key)

            if cached_result:
                self.performance_metrics["cache_hit_rate"] += 1
                await self._send_cached_result(user_id, cached_result)
                return cached_result

            # Get project and create temporary utility for analysis
            project = await self._get_project(self.project_id)
            temp_utility = await self._create_temp_utility(project, utility_data, geometry_data)

            # Initialize conflict detection engine
            detection_engine = ConflictDetectionEngine(project, context)

            # Perform conflict detection
            conflict_result = await sync_to_async(detection_engine.detect_conflicts_real_time)(
                temp_utility,
                geometry_data,
                context,
            )

            # Process and format results
            processed_result = await self._process_conflict_result(conflict_result, user_id, utility_data)

            # Cache result for performance
            cache.set(cache_key, processed_result, timeout=300)  # 5 minutes

            # Send real-time notifications
            await self._send_conflict_notifications(user_id, processed_result)

            # Update performance metrics
            execution_time = timezone.now() - start_time
            await self._update_performance_metrics(execution_time, cached=False)

            return processed_result

        except (ConnectionError, TimeoutError, AttributeError, KeyError) as e:
            logger.error(f"Real-time conflict detection failed: {e}")

            error_result = {
                "success": False,
                "error": str(e),
                "conflicts": [],
                "severity": "error",
                "timestamp": timezone.now().isoformat(),
            }

            await self._send_to_user(
                user_id,
                {
                    "type": "conflict_detection_error",
                    "result": error_result,
                },
            )

            return error_result

    async def start_collaborative_session(self, user_id: str, session_data: dict[str, Any]) -> str:
        """Start a collaborative drawing session.

        Args:
        ----
            user_id: ID of the user starting the session
            session_data: Configuration data for the collaboration session

        Returns:
        -------
            Session ID for the newly created collaborative session

        """
        session_id = f"collab_{self.project_id}_{user_id}_{timezone.now().timestamp()}"

        collaboration_session = {
            "session_id": session_id,
            "creator": user_id,
            "participants": [user_id],
            "created_at": timezone.now(),
            "session_data": session_data,
            "active": True,
        }

        # Store session in cache
        cache.set(f"collab_session_{session_id}", collaboration_session, timeout=7200)  # 2 hours

        # Notify all users about new collaboration session
        await self._broadcast_to_all(
            {
                "type": "collaboration_session_started",
                "session": collaboration_session,
            },
        )

        return session_id

    async def join_collaborative_session(self, user_id: str, session_id: str) -> bool:
        """Join an existing collaborative session.

        Args:
        ----
            user_id: ID of the user joining the session
            session_id: ID of the session to join

        Returns:
        -------
            True if successfully joined, False otherwise

        """
        session_key = f"collab_session_{session_id}"
        session = cache.get(session_key)

        if session and session["active"]:
            session["participants"].append(user_id)
            cache.set(session_key, session, timeout=7200)

            # Notify session participants
            await self._send_to_session_participants(
                session,
                {
                    "type": "user_joined_session",
                    "user_id": user_id,
                    "session_id": session_id,
                },
            )

            return True

        return False

    async def broadcast_drawing_action(self, user_id: str, action_type: str, action_data: dict[str, Any]) -> None:
        """Broadcast drawing actions to other connected users.

        Args:
        ----
            user_id: ID of the user performing the action
            action_type: Type of drawing action being performed
            action_data: Data associated with the drawing action

        """
        message = {
            "type": "drawing_action",
            "user_id": user_id,
            "action_type": action_type,
            "action_data": action_data,
            "timestamp": timezone.now().isoformat(),
        }

        # Send to all other users except the sender
        await self._broadcast_to_others(user_id, message)

        # Trigger conflict detection if geometry is being modified
        if action_type in ["draw_utility", "modify_utility", "move_utility"]:
            await self.detect_conflicts_real_time(
                user_id,
                action_data.get("utility_data", {}),
                action_data.get("geometry_data", {}),
            )

    async def resolve_conflict_real_time(self, user_id: str, conflict_id: str, resolution_data: dict[str, Any]) -> bool:
        """Handle real-time conflict resolution.

        Args:
        ----
            user_id: ID of the user resolving the conflict
            conflict_id: ID of the conflict to resolve
            resolution_data: Resolution information and notes

        Returns:
        -------
            True if successfully resolved, False otherwise

        """
        try:
            # Import runtime dependencies
            from apps.infrastructure.models.conflict_rules import ConflictDetectionEvent

            # Get conflict details
            conflict_event = await database_sync_to_async(ConflictDetectionEvent.objects.get)(id=conflict_id)

            # Apply resolution
            conflict_event.action_taken = resolution_data.get("action", "user_resolved")
            conflict_event.resolution_notes = resolution_data.get("notes", "")
            conflict_event.resolved_by_id = user_id
            conflict_event.resolved_at = timezone.now()

            await database_sync_to_async(conflict_event.save)()

            # Broadcast resolution to all users
            await self._broadcast_to_all(
                {
                    "type": "conflict_resolved",
                    "conflict_id": conflict_id,
                    "resolved_by": user_id,
                    "resolution": resolution_data,
                    "timestamp": timezone.now().isoformat(),
                },
            )

            return True

        except (ValidationError, ValueError) as e:
            logger.error(f"Conflict resolution failed: {e}")
            return False

    async def get_real_time_analytics(self) -> dict[str, Any]:
        """Get real-time analytics for the project"""
        # Get recent conflicts
        recent_conflicts = await database_sync_to_async(list)(
            ConflictDetectionEvent.objects.filter(
                project_id=self.project_id,
                created_at__gte=timezone.now() - timedelta(hours=1),
            ).order_by("-created_at")[:10],
        )

        # Calculate active users and sessions
        active_sessions = len(
            [
                session
                for session in self.drawing_sessions.values()
                if session["last_activity"] > timezone.now() - timedelta(minutes=5)
            ],
        )

        return {
            "active_users": len(self.active_users),
            "active_sessions": active_sessions,
            "recent_conflicts": [
                {
                    "id": str(conflict.id),
                    "severity": conflict.severity_level,
                    "confidence": float(conflict.confidence_score),
                    "resolved": conflict.resolved_at is not None,
                    "timestamp": conflict.created_at.isoformat(),
                }
                for conflict in recent_conflicts
            ],
            "performance_metrics": self.performance_metrics,
            "timestamp": timezone.now().isoformat(),
        }

    # Private helper methods

    async def _send_to_user(self, user_id: str, message: dict[str, Any]):
        """Send message to specific user"""
        if user_id in self.drawing_sessions:
            channel_name = self.drawing_sessions[user_id]["channel_name"]
            channel_layer = get_channel_layer()
            await channel_layer.send(
                channel_name,
                {
                    "type": "conflict_message",
                    "message": message,
                },
            )

    async def _broadcast_to_all(self, message: dict[str, Any]):
        """Broadcast message to all connected users"""
        for user_id in self.active_users:
            await self._send_to_user(user_id, message)

    async def _broadcast_to_others(self, sender_id: str, message: dict[str, Any]):
        """Broadcast message to all users except sender"""
        for user_id in self.active_users:
            if user_id != sender_id:
                await self._send_to_user(user_id, message)

    async def _send_to_session_participants(self, session: dict, message: dict[str, Any]):
        """Send message to all participants in a collaboration session"""
        for participant_id in session["participants"]:
            await self._send_to_user(participant_id, message)

    async def _get_project(self, project_id: str):
        """Get project instance asynchronously"""
        from apps.infrastructure.models import Project

        return await database_sync_to_async(Project.objects.get)(id=project_id)

    async def _create_temp_utility(self, project, utility_data: dict, geometry_data: dict):
        """Create temporary utility for conflict analysis"""
        from django.contrib.gis.geos import GEOSGeometry

        # Create geometry from data
        if geometry_data.get("type") == "Point":
            from django.contrib.gis.geos import Point

            coords = geometry_data["coordinates"]
            geometry = Point(coords[0], coords[1], srid=4326)
        else:
            geometry = GEOSGeometry(json.dumps(geometry_data), srid=4326)

        # Create temporary utility (not saved to database)
        return Utility(
            project=project,
            name=utility_data.get("name", "Temporary Utility"),
            type=utility_data.get("type", "unknown"),
            geometry=geometry,
        )

    async def _process_conflict_result(self, conflict_result, user_id: str, utility_data: dict) -> dict[str, Any]:
        """Process and format conflict detection result"""
        processed = {
            "success": True,
            "user_id": user_id,
            "utility_name": utility_data.get("name", "Unknown"),
            "utility_type": utility_data.get("type", "unknown"),
            "conflicts_found": conflict_result.conflicts_found,
            "severity": conflict_result.severity.value,
            "confidence": float(conflict_result.confidence),
            "execution_time_ms": conflict_result.execution_time.total_seconds() * 1000,
            "conflicts": [],
            "recommendations": conflict_result.recommendations,
            "regulatory_flags": conflict_result.regulatory_flags,
            "timestamp": timezone.now().isoformat(),
        }

        # Process individual conflicts
        for event_data in conflict_result.detection_events:
            processed["conflicts"].append(
                {
                    "rule_id": event_data.get("rule_id"),
                    "conflicts_detected": event_data.get("conflicts_detected", 0),
                    "confidence": event_data.get("confidence", 0),
                    "description": event_data.get("description", "Conflict detected"),
                },
            )

        return processed

    async def _send_conflict_notifications(self, user_id: str, result: dict[str, Any]):
        """Send conflict detection notifications to connected users"""
        # Send to the user who triggered the detection
        await self._send_to_user(
            user_id,
            {
                "type": "conflict_detection_result",
                "result": result,
            },
        )

        # If conflicts found, notify other users
        if result["conflicts_found"] > 0:
            notification = {
                "type": "conflict_detected_by_user",
                "detecting_user": user_id,
                "utility_name": result["utility_name"],
                "utility_type": result["utility_type"],
                "severity": result["severity"],
                "conflicts_count": result["conflicts_found"],
                "timestamp": result["timestamp"],
            }

            await self._broadcast_to_others(user_id, notification)

    async def _send_cached_result(self, user_id: str, cached_result: dict[str, Any]):
        """Send cached conflict detection result"""
        cached_result["cached"] = True
        cached_result["timestamp"] = timezone.now().isoformat()

        await self._send_to_user(
            user_id,
            {
                "type": "conflict_detection_result",
                "result": cached_result,
            },
        )

    async def _update_performance_metrics(self, execution_time: timedelta, cached: bool):
        """Update performance metrics"""
        self.performance_metrics["total_detections"] += 1

        if not cached:
            # Update average execution time
            current_avg = self.performance_metrics["avg_detection_time"]
            total_detections = self.performance_metrics["total_detections"]
            new_time = execution_time.total_seconds() * 1000  # Convert to ms

            self.performance_metrics["avg_detection_time"] = (
                current_avg * (total_detections - 1) + new_time
            ) / total_detections

        # Update cache hit rate
        cache_hits = self.performance_metrics.get("cache_hit_rate", 0)
        self.performance_metrics["cache_hit_rate"] = cache_hits / self.performance_metrics["total_detections"] * 100

    def _generate_cache_key(self, utility_data: dict, geometry_data: dict) -> str:
        """Generate cache key for conflict detection results"""
        import hashlib

        key_components = [
            utility_data.get("type", "unknown"),
            json.dumps(geometry_data, sort_keys=True),
            self.project_id,
        ]

        key_string = "|".join(key_components)
        return f"realtime_conflict:{hashlib.sha256(key_string.encode()).hexdigest()}"


class ConflictDetectionConsumer(AsyncWebsocketConsumer):
    """WebSocket consumer for real-time conflict detection.

    Handles WebSocket connections for live conflict detection during
    drawing operations and collaborative sessions.
    """

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.project_id = None
        self.user_id = None
        self.service = None
        self.group_name = None

    async def connect(self):
        """Handle WebSocket connection"""
        # Extract project ID from URL
        self.project_id = self.scope["url_route"]["kwargs"]["project_id"]
        self.user_id = str(self.scope["user"].id) if self.scope["user"].is_authenticated else None

        if not self.user_id:
            await self.close()
            return

        # Create group name for this project
        self.group_name = f"conflict_detection_{self.project_id}"

        # Join project group
        await self.channel_layer.group_add(self.group_name, self.channel_name)

        # Initialize real-time service
        self.service = RealTimeConflictService(self.project_id)

        # Register user connection
        await self.service.user_connected(self.user_id, self.channel_name)

        await self.accept()

        logger.info(
            f"ConflictDetectionConsumer connected: user={self.user_id}, project={self.project_id}",
        )

    async def disconnect(self, close_code):
        """Handle WebSocket disconnection"""
        if self.service and self.user_id:
            await self.service.user_disconnected(self.user_id)

        # Leave project group
        if self.group_name:
            await self.channel_layer.group_discard(self.group_name, self.channel_name)

        logger.info(
            f"ConflictDetectionConsumer disconnected: user={self.user_id}, code={close_code}",
        )

    async def receive(self, text_data):
        """Handle incoming WebSocket messages"""
        try:
            data = json.loads(text_data)
            message_type = data.get("type")

            if message_type == "detect_conflicts":
                await self._handle_conflict_detection(data)
            elif message_type == "drawing_action":
                await self._handle_drawing_action(data)
            elif message_type == "start_collaboration":
                await self._handle_start_collaboration(data)
            elif message_type == "join_collaboration":
                await self._handle_join_collaboration(data)
            elif message_type == "resolve_conflict":
                await self._handle_resolve_conflict(data)
            elif message_type == "get_analytics":
                await self._handle_get_analytics(data)
            elif message_type == "ping":
                await self._handle_ping(data)
            else:
                logger.warning(f"Unknown message type: {message_type}")

        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON received: {e}")
            await self.send(
                text_data=json.dumps(
                    {
                        "type": "error",
                        "message": "Invalid JSON format",
                    },
                ),
            )
        except (json.JSONDecodeError, ValueError) as e:
            logger.error(f"Error handling WebSocket message: {e}")
            await self.send(
                text_data=json.dumps(
                    {
                        "type": "error",
                        "message": str(e),
                    },
                ),
            )

    async def _handle_conflict_detection(self, data):
        """Handle conflict detection request"""
        utility_data = data.get("utility_data", {})
        geometry_data = data.get("geometry_data", {})

        result = await self.service.detect_conflicts_real_time(self.user_id, utility_data, geometry_data)

        await self.send(
            text_data=json.dumps(
                {
                    "type": "conflict_detection_result",
                    "result": result,
                },
            ),
        )

    async def _handle_drawing_action(self, data):
        """Handle drawing action broadcast"""
        action_type = data.get("action_type")
        action_data = data.get("action_data", {})

        await self.service.broadcast_drawing_action(self.user_id, action_type, action_data)

    async def _handle_start_collaboration(self, data):
        """Handle collaboration session start"""
        session_data = data.get("session_data", {})

        session_id = await self.service.start_collaborative_session(self.user_id, session_data)

        await self.send(
            text_data=json.dumps(
                {
                    "type": "collaboration_session_created",
                    "session_id": session_id,
                },
            ),
        )

    async def _handle_join_collaboration(self, data):
        """Handle joining collaboration session"""
        session_id = data.get("session_id")

        success = await self.service.join_collaborative_session(self.user_id, session_id)

        await self.send(
            text_data=json.dumps(
                {
                    "type": "collaboration_join_result",
                    "success": success,
                    "session_id": session_id,
                },
            ),
        )

    async def _handle_resolve_conflict(self, data):
        """Handle conflict resolution"""
        conflict_id = data.get("conflict_id")
        resolution_data = data.get("resolution_data", {})

        success = await self.service.resolve_conflict_real_time(self.user_id, conflict_id, resolution_data)

        await self.send(
            text_data=json.dumps(
                {
                    "type": "conflict_resolution_result",
                    "success": success,
                    "conflict_id": conflict_id,
                },
            ),
        )

    async def _handle_get_analytics(self, data):
        """Handle analytics request"""
        analytics = await self.service.get_real_time_analytics()

        await self.send(
            text_data=json.dumps(
                {
                    "type": "analytics_data",
                    "analytics": analytics,
                },
            ),
        )

    async def _handle_ping(self, data):
        """Handle ping/keepalive message"""
        await self.send(
            text_data=json.dumps(
                {
                    "type": "pong",
                    "timestamp": timezone.now().isoformat(),
                },
            ),
        )

    # Group message handlers

    async def conflict_message(self, event):
        """Handle messages sent to the conflict detection group"""
        message = event["message"]
        await self.send(text_data=json.dumps(message))


# Global service registry for managing active services
_active_services: dict[str, RealTimeConflictService] = {}


def get_realtime_service(project_id: str) -> RealTimeConflictService:
    """Get or create real-time conflict service for a project"""
    if project_id not in _active_services:
        _active_services[project_id] = RealTimeConflictService(project_id)
    return _active_services[project_id]


def cleanup_inactive_services():
    """Clean up inactive services (called periodically)"""
    current_time = timezone.now()
    inactive_projects = []

    for project_id, service in _active_services.items():
        # Check if service has been inactive for more than 1 hour
        if not service.active_users:
            last_activity = max(
                (session["last_activity"] for session in service.drawing_sessions.values()),
                default=current_time - timedelta(hours=2),
            )

            if current_time - last_activity > timedelta(hours=1):
                inactive_projects.append(project_id)

    # Remove inactive services
    for project_id in inactive_projects:
        del _active_services[project_id]
        logger.info(f"Cleaned up inactive real-time service for project {project_id}")


# Import channel layer
try:
    from channels.layers import get_channel_layer
except ImportError:
    # Fallback if channels not available
    def get_channel_layer():
        return None
